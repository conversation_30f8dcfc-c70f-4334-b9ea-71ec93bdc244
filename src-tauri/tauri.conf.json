{"$schema": "https://schema.tauri.app/config/2", "productName": "Switch.AI", "version": "1.0.0", "identifier": "com.switchai.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "Switch AI Browser", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false}], "security": {"csp": "default-src 'self'; connect-src https:; script-src 'self'; style-src 'self' 'unsafe-inline'"}}, "bundle": {"active": true, "targets": ["dmg", "msi", "appimage"], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}