import { useEffect, useState } from 'react';
import { useAppStore } from '../store';

export const WebView = () => {
  const { activePlatform } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [iframeKey, setIframeKey] = useState(0);

  useEffect(() => {
    if (activePlatform) {
      setIsLoading(true);
      setIframeKey((prev) => prev + 1);
      const timer = setTimeout(() => setIsLoading(false), 500);
      return () => clearTimeout(timer);
    }
  }, [activePlatform]);

  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-bgPrimary to-bgTertiary">
      <div className="text-center space-y-6 max-w-md mx-auto px-6">
        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
          <span className="text-white text-2xl font-bold">AI</span>
        </div>
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-textPrimary">Select a Platform</h3>
          <p className="text-textSecondary leading-relaxed">
            Choose a platform from the sidebar to begin. Your selected web app will load here.
          </p>
        </div>
      </div>
    </div>
  );

  if (!activePlatform) return <EmptyState />;

  return (
    <main className="flex-1 bg-bgPrimary relative flex flex-col">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-bgPrimary z-20">
          <div className="w-16 h-16 border-4 border-accentStart border-t-transparent rounded-full animate-spin" />
        </div>
      )}
      <iframe
        key={iframeKey}
        src={activePlatform.url}
        title={activePlatform.name}
        className={`w-full h-full border-none transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'}`}
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
      />
    </main>
  );
};
