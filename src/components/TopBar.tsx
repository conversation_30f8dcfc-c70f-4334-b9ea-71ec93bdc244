import { useAppStore } from '../store';

const modes = [
  { key: 'chat', label: 'Chat' },
  { key: 'create', label: 'Create' },
  { key: 'lab', label: 'Lab' },
  { key: 'hub', label: 'Hub' },
  { key: 'settings', label: 'Settings' }
] as const;

export const TopBar = () => {
  const { activeMode, setMode } = useAppStore();

  return (
    <div className="h-16 flex items-center justify-between px-6 border-b border-border bg-bgPrimary/80 backdrop-blur-xl">
      {/* Logo */}
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-gradient-to-br from-accentStart to-accentEnd rounded-lg flex items-center justify-center">
          <span className="text-white font-bold text-sm">S</span>
        </div>
        <h1 className="text-xl font-bold bg-gradient-to-r from-accentStart to-accentEnd bg-clip-text text-transparent">
          Switch.AI
        </h1>
      </div>

      {/* Button Group Navigation */}
      <nav className="flex items-center bg-bgSecondary p-1 rounded-lg border border-border">
        {modes.map((mode) => {
          const isActive = activeMode === mode.key;
          return (
            <button
              key={mode.key}
              onClick={() => setMode(mode.key)}
              className={`px-4 py-1.5 text-sm font-semibold rounded-md transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-accentStart focus-visible:ring-opacity-50 ${
                isActive
                  ? 'bg-bgTertiary text-textPrimary shadow-sm'
                  : 'bg-transparent text-textSecondary hover:text-textPrimary'
              }`}
            >
              {mode.label}
            </button>
          );
        })}
      </nav>

      {/* Empty div to balance layout */}
      <div className="w-24" />
    </div>
  );
};
