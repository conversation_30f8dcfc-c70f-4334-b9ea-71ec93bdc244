import { platforms } from '../data/platforms';
import { useAppStore } from '../store';
import { useState } from 'react';

export const Sidebar = () => {
  const { activeMode, activePlatform, setPlatform } = useAppStore();
  const [hoveredPlatform, setHoveredPlatform] = useState<string | null>(null);
  const list = platforms.filter((p) => p.mode === activeMode);

  return (
    <aside className="w-sidebar bg-bgSecondary border-r border-border flex flex-col items-center py-6 space-y-4 scrollbar-thin overflow-y-auto">
      {list.length === 0 ? (
        <div className="text-textTertiary text-xs text-center px-2">
          No platforms available
        </div>
      ) : (
        list.map((p) => {
          const isSelected = activePlatform?.id === p.id;
          const isHovered = hoveredPlatform === p.id;

          return (
            <div
              key={p.id}
              onClick={() => setPlatform(p)}
              onMouseEnter={() => setHoveredPlatform(p.id)}
              onMouseLeave={() => setHoveredPlatform(null)}
              className="group relative"
            >
              <div className={`platform-card ${isSelected ? 'selected' : ''}`}>
                {/* Unified Icon Background */}
                <div className={`absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 opacity-20 group-hover:opacity-30 transition-opacity duration-300`} />

                {/* Icon */}
                <img
                  src={`/icons/${p.iconFile}`}
                  alt={p.name}
                  className={`w-8 h-8 relative z-10 transition-all duration-300 ${
                    isSelected ? 'drop-shadow-lg' : ''
                  } ${isHovered ? 'scale-110' : ''}`}
                />

                {/* Selection Indicator */}
                {isSelected && (
                  <div className="absolute -right-1.5 top-1/2 -translate-y-1/2 w-2 h-6 bg-accentStart rounded-full border-2 border-bgSecondary" />
                )}
              </div>

              {/* Bigger Tooltip */}
              <div className={`absolute left-full ml-4 top-1/2 -translate-y-1/2 bg-bgTertiary text-textPrimary text-base px-4 py-2 rounded-xl border border-border shadow-xl z-50 whitespace-nowrap transition-all duration-300 pointer-events-none ${
                isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2'
              }`}>
                {p.name}
              </div>
            </div>
          );
        })
      )}
    </aside>
  );
};
